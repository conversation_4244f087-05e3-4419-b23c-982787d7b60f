:: Copyright Digital Domain, Inc. All Rights Reserved.
@echo off

@Rem Parsing arguments
set "resX=5120"
set "resY=2880"
set "disablecheck=0"
set "portnum=8888"
:loop
IF NOT "%1"=="" (
  IF "%1"=="--portrait" (
    SET "resX=1080"
    SET "resY=1920"
  )
  IF "%~1"=="--square" (
    SET "resX=1920"
    SET "resY=1920"
  )
  IF "%~1"=="--disablecheck" (
    SET "disablecheck=1"
  )
  IF "%~1"=="--portnum" (
    SET "portnum=%~2"
    SHIFT
  )
  SHIFT
  GOTO :loop
)
echo wss port number: %portnum%
echo disable check running porcess: %disablecheck%

IF %disablecheck% == 0 (
  setlocal enabledelayedexpansion
  tasklist /fi "ImageName eq UE4Editor.exe" /fo csv 2>NUL | find /I "UE4Editor.exe">NUL
  if "!errorlevel!"=="0" (
    echo WScript.Quit msgBox("Please close UE4Editor.exe before running UE streaming", vbOKOnly, "MomentumCloud"^) > %temp%\msgbox.vbs
    start /wait %temp%\msgbox.vbs
    exit /b
  )

  tasklist /fi "ImageName eq VirtualHuman.exe" /fo csv 2>NUL | find /I "VirtualHuman.exe">NUL
  if "!errorlevel!"=="0" (
    echo WScript.Quit msgBox("VirtualHuman.exe is already running. Do you want to restart it?", vbYesNo, "MomentumCloud"^) > %temp%\msgbox.vbs
    start /wait %temp%\msgbox.vbs
    REM Yes=6, No=7
    if "!errorlevel!"=="7" (
      exit /b
    )
    taskkill /IM "VirtualHuman.exe" /F /T
  )
  endlocal
)

@Rem Move to Renderer directory
pushd "%~dp0\..\Renderer"

@Rem Calculate half resolution
set /a halfResX=%resX%/2

@Rem Start streaming
start VirtualHuman.exe -AudioMixer -AllowPixelStreamingCommands -PixelStreamingDDUseDDAudioDevice -Windowed -ForceRes -WinX=0 -WinY=0 -ResX=%halfResX% -ResY=%resY% -NotInstalled -PixelStreamingEncoderCodec=AV1

popd
